from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.decorators import action
from utils.pagination import CustomPagination
from drf_spectacular.utils import extend_schema
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.parsers import <PERSON>PartParser, FormParser, JSONParser
from rest_framework.response import Response
from django.db.models import Q
from django.db import transaction
from datetime import datetime

from .models import ManpowerQualifications
from .serializers import ManpowerQualificationsSerializer
from users.models import User
import pandas as pd
import io
import urllib.parse
from django.http import HttpResponse


@extend_schema(
    tags=["Manpower"]
)
class ManpowerQualificationsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ManpowerQualifications model providing CRUD operations.
    """
    queryset = ManpowerQualifications.objects.all()
    serializer_class = ManpowerQualificationsSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ars<PERSON>, JSONParser)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['code','name','organization','qualification','fieldOfStudy','experienceYears','skill','isCertification','status']
    search_fields = ['code','name','organization','qualification','fieldOfStudy','experienceYears','skill','isCertification','status']
    
    def list(self, request, *args, **kwargs):
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        organization = request.query_params.get('organization')
        qualification = request.query_params.get('qualification')
        fieldOfStudy = request.query_params.get('fieldOfStudy')
        experienceYears = request.query_params.get('experienceYears')
        skill = request.query_params.get('skill')
        isCertification = request.query_params.get('isCertification')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        queryset = self.get_queryset()
        if code:
            queryset = queryset.filter(code__icontains=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if organization:
            queryset = queryset.filter(organization__icontains=organization)
        if qualification:
            queryset = queryset.filter(qualification=qualification)
        if fieldOfStudy:
            queryset = queryset.filter(fieldOfStudy__icontains=fieldOfStudy)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if skill:
            queryset = queryset.filter(skill__icontains=skill)
        if isCertification:
            queryset = queryset.filter(isCertification=isCertification)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            queryset = queryset.order_by(ordering)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_manpower_qualifications(self, request, *args, **kwargs):
        """
        Import ManpowerQualifications from list data
        Request Body:
        [
            {
                "code": "ตัวอย่างรหัสบุคลากร",
                "organization": "ตัวสังกัดหน่วยงาน",
                "name": "ตัวอย่างชื่อ-นามสกุล",
                "qualification": "ตัวอย่างวุฒิการศึกษา",
                "fieldOfStudy": "ตัวอย่างสาขาวิชา",
                "experienceYears": "ตัวอย่างปีประสบการณ์",
                "skill": "ตัวอย่างทักษะเฉพาะด้าน",
                "isCertification": "ตัวอย่างใบรับรองวิชาชีพ"
            }
        ]
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of manpower qualification objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_records = []
        errors = []

        # Qualification mapping based on data dictionary
        qualification_mapping = {
            'ปริญญาตรี': 'B',
            'ปริญญาโท': 'M',
            'ปริญญาเอก': 'D',
            'ปวช.': 'V',
            'ปวส.': 'H',
            'B': 'B',
            'M': 'M',
            'D': 'D',
            'V': 'V',
            'H': 'H'
        }

        # Certification mapping
        certification_mapping = {
            'มี': True,
            'ไม่มี': False,
            '1': True,
            '0': False,
            True: True,
            False: False
        }

        with transaction.atomic():
            for index, item in enumerate(request.data):
                try:
                    # Validate required fields
                    required_fields = ['code', 'name', 'organization', 'qualification', 'fieldOfStudy', 'experienceYears', 'skill', 'isCertification']
                    missing_fields = [field for field in required_fields if not item.get(field)]

                    if missing_fields:
                        errors.append({
                            'index': index,
                            'error': f'Missing required fields: {", ".join(missing_fields)}',
                            'data': item
                        })
                        continue

                    # Map qualification
                    qualification_input = str(item.get('qualification', '')).strip()
                    qualification = qualification_mapping.get(qualification_input)
                    if not qualification:
                        qualification = 'B'

                    # Map certification
                    certification_input = item.get('isCertification')
                    if isinstance(certification_input, str):
                        certification_input = certification_input.strip()

                    is_certification = certification_mapping.get(certification_input)
                    if is_certification is None:
                        is_certification = 0

                    # Validate experience years
                    try:
                        experience_years = int(item.get('experienceYears', 0))
                        if experience_years < 0:
                            raise ValueError("Experience years cannot be negative")
                    except (ValueError, TypeError):
                        experience_years = 0

                    # Check if code already exists
                    if ManpowerQualifications.objects.filter(code=item.get('code')).exists():
                        errors.append({
                            'index': index,
                            'error': f'Code "{item.get("code")}" already exists',
                            'data': item
                        })
                        continue

                    # Create the record
                    manpower_qualification = ManpowerQualifications.objects.create(
                        code=item.get('code').strip(),
                        name=item.get('name').strip(),
                        organization=item.get('organization').strip(),
                        qualification=qualification,
                        fieldOfStudy=item.get('fieldOfStudy').strip(),
                        experienceYears=experience_years,
                        skill=item.get('skill').strip(),
                        isCertification=is_certification,
                        status=True  # Default to active
                    )

                    created_records.append(manpower_qualification)

                except Exception as e:
                    errors.append({
                        'index': index,
                        'error': f'Unexpected error: {str(e)}',
                        'data': item
                    })

        # Prepare response
        response_data = {
            "message": f"Successfully imported {len(created_records)} manpower qualification records",
            "created_count": len(created_records),
            "error_count": len(errors)
        }

        if created_records:
            serializer = self.get_serializer(created_records, many=True)
            response_data["created_records"] = serializer.data

        if errors:
            response_data["errors"] = errors

        # Return appropriate status code
        if errors and not created_records:
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        elif errors and created_records:
            return Response(response_data, status=status.HTTP_207_MULTI_STATUS)
        else:
            return Response(response_data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลผู้เชี่ยวชาญระบบราง</h1>
        """
        
        ids = request.data.get('ids')
        code = request.data.get('code')
        name = request.data.get('name')
        organization = request.data.get('organization')
        qualification = request.data.get('qualification')
        fieldOfStudy = request.data.get('fieldOfStudy')
        experienceYears = request.data.get('experienceYears')
        skill = request.data.get('skill')
        isCertification = request.data.get('isCertification')
        status = request.data.get('status')
        userId = request.data.get('userId')
        
        queryset = self.get_queryset()
        if code:
            queryset = queryset.filter(code__icontains=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if organization:
            queryset = queryset.filter(organization__icontains=organization)
        if qualification:
            queryset = queryset.filter(qualification=qualification)
        if fieldOfStudy:
            queryset = queryset.filter(fieldOfStudy__icontains=fieldOfStudy)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if skill:
            queryset = queryset.filter(skill__icontains=skill)
        if isCertification:
            queryset = queryset.filter(isCertification=isCertification)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
                
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)
        
        qualification_mapping = {
            'B': 'ปริญญาตรี',
            'M': 'ปริญญาโท',
            'D': 'ปริญญาเอก',
            'V': 'ปวช.',
            'H': 'ปวส.'
        }

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'รหัสบุคลากร': item.code,
                'สังกัดหน่วยงาน': item.organization,
                'ชื่อ-นามสกุล': item.name,
                'วุฒิการศึกษา': qualification_mapping.get(item.qualification, item.qualification),
                'สาขาวิชา': item.fieldOfStudy,
                'ปีประสบการณ์': item.experienceYears,
                'ทักษะเฉพาะด้าน': item.skill,
                'ใบรับรองวิชาชีพ': 'มี' if item.isCertification else 'ไม่มี',
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลผู้เชี่ยวชาญระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:J1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:J2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
