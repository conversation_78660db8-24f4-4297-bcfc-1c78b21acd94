from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from django.db.models import Q
from django.db import transaction
from datetime import datetime
from testings.models import Runningnumber
from .models import Expert
from .serializers import ExpertSerializer
from users.models import User
import pandas as pd
import io
import urllib.parse
from django.http import HttpResponse
from django.conf import settings


@extend_schema(
    tags=["Expert"]
)
class ExpertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Expert model providing CRUD operations.
    Supports file uploads for certifications field.
    """
    queryset = Expert.objects.all()
    serializer_class = ExpertSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['code','name','affiliation','specialization','experienceYears','status']
    search_fields = ['code','name','affiliation','specialization','experienceYears','status']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        code = request.query_params.get('code')
        name = request.query_params.get('name')
        affiliation = request.query_params.get('affiliation')
        specialization = request.query_params.get('specialization')
        experienceYears = request.query_params.get('experienceYears')
        status = request.query_params.get('status')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        if code:
            queryset = queryset.filter(code=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if affiliation:
            queryset = queryset.filter(affiliation__icontains=affiliation)
        if specialization:
            queryset = queryset.filter(specialization=specialization)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new Expert with auto-generated code
        """
        try:
            with transaction.atomic():
                # Get the last running number for Expert (type 'E')
                lastRunningNumber = Runningnumber.objects.select_for_update().filter(type='E').order_by('-number').first()
                if lastRunningNumber:
                    nextNumber = lastRunningNumber.number + 1
                else:
                    nextNumber = 1

                # Create a mutable copy of request data and add code
                data = request.data.copy()
                data['code'] = f"E{nextNumber:04d}"

                # Create the expert
                serializer = self.get_serializer(data=data)
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)

                # If we got here, creation was successful - update running number
                if lastRunningNumber:
                    lastRunningNumber.number = nextNumber
                    lastRunningNumber.save()
                else:
                    # Create new running number if it doesn't exist
                    current_year = datetime.now().year
                    Runningnumber.objects.create(
                        type='E',
                        year=current_year,
                        number=nextNumber
                    )

                headers = self.get_success_headers(serializer.data)
                return Response(serializer.data, status=201, headers=headers)
        except Exception as e:
            # Log the error and re-raise it
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating Expert: {str(e)}")
            raise

    @action(detail=False, methods=['post'], url_path='import')
    def import_experts(self, request):
        """
        Import multiple experts from a list with auto-generated codes
        Expected data format:
        [
            {
                "name": "ตัวชื่อ - นามสกุล",
                "affiliation": "ตัวอย่างหน่วยงาน/บริษัท",
                "specialization": "ตัวอย่างความเชี่ยวชาญ",
                "experienceYears": "ตัวอย่างจำนวนปีประสบการณ์",
                "phone": "ตัวอย่างเบอร์โทรติดต่อ",
                "email": "ตัวอีเมลติดต่อ",
                "remark": "ตัวอย่างหมายเหตุ"
            }
        ]
        """
        try:
            experts_data = request.data

            # Validate that the data is a list
            if not isinstance(experts_data, list):
                return Response(
                    {"error": "Data must be a list of expert objects"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not experts_data:
                return Response(
                    {"error": "List cannot be empty"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            created_experts = []
            errors = []

            with transaction.atomic():
                # Get the last running number for Expert (type 'E')
                lastRunningNumber = Runningnumber.objects.select_for_update().filter(type='E').order_by('-number').first()
                if lastRunningNumber:
                    nextNumber = lastRunningNumber.number + 1
                else:
                    nextNumber = 1

                for index, expert_data in enumerate(experts_data):
                    try:
                        # Create a mutable copy and add auto-generated code
                        data = expert_data.copy()
                        data['code'] = f"E{nextNumber:04d}"

                        # Check and validate experienceYears field
                        if 'experienceYears' in data:
                            experience_years = data['experienceYears']
                            # Check if it's a valid integer
                            if isinstance(experience_years, str):
                                # Try to convert string to integer
                                try:
                                    data['experienceYears'] = int(experience_years)
                                except (ValueError, TypeError):
                                    # If conversion fails, set to default 0
                                    data['experienceYears'] = 0
                            elif not isinstance(experience_years, int):
                                # If it's not string or int, set to default 0
                                data['experienceYears'] = 0
                        else:
                            # If experienceYears field is missing, set to default 0
                            data['experienceYears'] = 0

                        # Create the expert
                        serializer = self.get_serializer(data=data)
                        if serializer.is_valid():
                            serializer.save()
                            created_experts.append(serializer.data)
                            nextNumber += 1
                        else:
                            errors.append({
                                "index": index,
                                "data": expert_data,
                                "errors": serializer.errors
                            })
                    except Exception as e:
                        errors.append({
                            "index": index,
                            "data": expert_data,
                            "error": str(e)
                        })

                # If there are any errors, rollback the transaction
                if errors:
                    transaction.set_rollback(True)
                    return Response(
                        {
                            "error": "Import failed due to validation errors",
                            "errors": errors,
                            "created_count": 0,
                            "total_count": len(experts_data)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Update the running number
                if lastRunningNumber:
                    lastRunningNumber.number = nextNumber - 1
                    lastRunningNumber.save()
                else:
                    # Create new running number if it doesn't exist
                    current_year = datetime.now().year
                    Runningnumber.objects.create(
                        type='E',
                        year=current_year,
                        number=nextNumber - 1
                    )

            return Response(
                {
                    "message": "Experts imported successfully",
                    "created_experts": created_experts,
                    "created_count": len(created_experts),
                    "total_count": len(experts_data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error importing experts: {str(e)}")
            return Response(
                {"error": f"Import failed: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลความปลอดภัย</h1>
        """
        
        ids = request.data.get('ids')
        code = request.data.get('code')
        name = request.data.get('name')
        affiliation = request.data.get('affiliation')
        specialization = request.data.get('specialization')
        experienceYears = request.data.get('experienceYears')
        status = request.data.get('status')
        userId = request.data.get('userId')
        
        queryset = self.get_queryset()
        if code:
            queryset = queryset.filter(code=code)
        if name:
            queryset = queryset.filter(name__icontains=name)
        if affiliation:
            queryset = queryset.filter(affiliation__icontains=affiliation)
        if specialization:
            queryset = queryset.filter(specialization=specialization)
        if experienceYears:
            queryset = queryset.filter(experienceYears__icontains=experienceYears)
        if status:
            queryset = queryset.filter(status=status)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
                
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        base_url = settings.MEDIA_URL
        if not base_url.startswith('http'):
            base_url = request.build_absolute_uri('/').rstrip('/') + '/' + settings.MEDIA_PREFIX
        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'รหัสผู้เชี่ยวชาญ': item.code,
                'ชื่อ - นามสกุล': item.name,
                'หน่วยงาน/บริษัท': item.affiliation,
                'ความเชี่ยวชาญ': item.specialization,
                'จำนวนปีประสบการณ์': str(item.experienceYears) + " ปี",
                'เบอร์โทรติดต่อ': item.phone,
                'อีเมลล์ติดต่อ': item.email,
                'หนังสือรับรอง/วุฒิบัตร': f"{base_url}{str(item.certifications)}",
                'หมายเหตุ': item.remark,
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลผู้เชี่ยวชาญระบบราง'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:J1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:J2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response


