from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import transaction
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time, convert_str_to_date
from django.db.models import Q
from datetime import datetime
from django.http import HttpResponse
import pandas as pd
import io
import urllib.parse

from .models import Safety
from .serializers import SafetySerializer
from users.models import User


class CustomPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


@extend_schema(
    tags=["Safety"]
)
class SafetyViewSet(viewsets.ModelViewSet):
    queryset = Safety.objects.all()
    serializer_class = SafetySerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['systemName', 'safetyFeature', 'safetyStandard','riskLevel','complianceStatus']
    search_fields = ['systemName', 'safetyFeature', 'safetyStandard','riskLevel','complianceStatus']

    def list(self, request, *args, **kwargs):
        systemName = request.query_params.get('systemName')
        safetyFeature = request.query_params.get('safetyFeature')
        safetyStandard = request.query_params.get('safetyStandard')
        riskLevel = request.query_params.get('riskLevel')
        complianceStatus = request.query_params.get('complianceStatus')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')

        if systemName:
            self.queryset = self.queryset.filter(systemName__icontains=systemName)
        if safetyFeature:
            self.queryset = self.queryset.filter(safetyFeature__icontains=safetyFeature)
        if safetyStandard:
            self.queryset = self.queryset.filter(safetyStandard__icontains=safetyStandard)
        if riskLevel:
            self.queryset = self.queryset.filter(riskLevel=riskLevel)
        if complianceStatus:
            self.queryset = self.queryset.filter(complianceStatus__icontains=complianceStatus)
        if startDate:
            self.queryset = self.queryset.filter(lastAuditDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            self.queryset = self.queryset.filter(lastAuditDate__lte=convert_str_to_date_max_time(endDate))
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            self.queryset = self.queryset.order_by(ordering)

        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_safety(self, request, *args, **kwargs):
        """
        Import safety data from list data
        Request Body:
        [
            {
                "systemName": "ตัวอย่างชื่อระบบราง",
                "safetyFeature": "ตัวรายการด้านความปลอดภัย",
                "safetyStandard": "ตัวอย่างมาตรฐานที่อ้างอิง",
                "riskLevel": "ตัวอย่างระดับความเสี่ยง",
                "lastAuditDate": "ตัวอย่างวันที่ตรวจสอบล่าสุด",
                "complianceStatus": "ตัวอย่างสถานะการปฏิบัติตามมาตรฐาน",
                "remark": "ตัวอย่างหมายเหตุ"
            }
        ]
        """
        if not isinstance(request.data, list):
            return Response(
                {"error": "Request data must be a list of safety objects"},
                status=status.HTTP_400_BAD_REQUEST
            )

        created_safety_records = []
        errors = []

        try:
            with transaction.atomic():
                for index, item in enumerate(request.data):
                    try:
                        # Validate required fields
                        required_fields = ['systemName', 'safetyFeature', 'safetyStandard', 'riskLevel']
                        missing_fields = []

                        for field in required_fields:
                            if not item.get(field):
                                missing_fields.append(field)

                        if missing_fields:
                            errors.append({
                                'index': index,
                                'error': f'Missing required fields: {", ".join(missing_fields)}'
                            })
                            continue
                        
                        if not item.get('riskLevel') or item['riskLevel'].upper() not in ['L', 'M', 'H', 'LOW', 'MEDIUM', 'HIGH']:
                            item['riskLevel'] = 'L'

                        # Create Safety record
                        safety_record = Safety.objects.create(
                            systemName=item['systemName'],
                            safetyFeature=item['safetyFeature'],
                            safetyStandard=item['safetyStandard'],
                            riskLevel=item['riskLevel'],
                            lastAuditDate=convert_str_to_date(item['lastAuditDate']),
                            complianceStatus=item.get('complianceStatus', ''),
                            remark=item.get('remark', '')
                        )

                        created_safety_records.append(safety_record)

                    except Exception as e:
                        errors.append({
                            'index': index,
                            'error': str(e)
                        })

                if errors:
                    # If there are errors, rollback the transaction
                    raise Exception("Import failed due to errors")

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "details": errors if errors else [str(e)]
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the created records for response
        serializer = SafetySerializer(created_safety_records, many=True)

        return Response(
            {
                "success": True,
                "message": f"Successfully imported {len(created_safety_records)} safety records",
                "created_records": serializer.data
            },
            status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลความปลอดภัย</h1>
        """
        
        ids = request.data.get('ids')
        systemName = request.data.get('systemName')
        safetyFeature = request.data.get('safetyFeature')
        safetyStandard = request.data.get('safetyStandard')
        riskLevel = request.data.get('riskLevel')
        complianceStatus = request.data.get('complianceStatus')
        startDate = request.data.get('startDate')
        endDate = request.data.get('endDate')
        userId = request.data.get('userId')
        
        queryset = self.get_queryset()
        if systemName:
            self.queryset = self.queryset.filter(systemName__icontains=systemName)
        if safetyFeature:
            self.queryset = self.queryset.filter(safetyFeature__icontains=safetyFeature)
        if safetyStandard:
            self.queryset = self.queryset.filter(safetyStandard__icontains=safetyStandard)
        if riskLevel:
            self.queryset = self.queryset.filter(riskLevel=riskLevel)
        if complianceStatus:
            self.queryset = self.queryset.filter(complianceStatus__icontains=complianceStatus)
        if startDate:
            self.queryset = self.queryset.filter(lastAuditDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            self.queryset = self.queryset.filter(lastAuditDate__lte=convert_str_to_date_max_time(endDate))
        if userId:
            self.queryset = self.queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
                
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'ชื่อระบบราง': item.systemName,
                'รายการด้านความปลอดภัย': item.safetyFeature,
                'มาตรฐานที่อ้างอิง': item.safetyStandard,
                'ระดับความเสี่ยง': item.riskLevel,
                'วันที่ตรวจสอบล่าสุด': item.lastAuditDate.strftime('%d/%m/%Y') if item.lastAuditDate else None,
                'สถานะการปฏิบัติตามมาตรฐาน': item.complianceStatus,
                'หมายเหตุ': item.remark,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลความปลอดภัย'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:J1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:J2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
  
