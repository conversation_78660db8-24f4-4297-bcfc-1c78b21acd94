from rest_framework import viewsets, filters, status
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import Environment
from .serializers import EnvironmentSerializer
from rest_framework.response import Response
from django.db.models import Q
from utils.util import convert_str_to_date_min_time, convert_str_to_date_max_time
from datetime import datetime
from users.models import User
import pandas as pd
import io
import urllib.parse
from django.http import HttpResponse



@extend_schema(
    tags=["Environment"]
)
class EnvironmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Environment model providing CRUD operations.
    """
    queryset = Environment.objects.all()
    serializer_class = EnvironmentSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = [JSONParser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['route__id','masProvince__id','masDistrict__id','parameter','value','unit','complianceStandard']
    search_fields = ['route__id','masProvince__id','masDistrict__id','parameter','value','unit','complianceStandard']

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        route__id = request.query_params.get('route__id')
        masProvince__id = request.query_params.get('masProvince__id')
        masDistrict__id = request.query_params.get('masDistrict__id')
        parameter = request.query_params.get('parameter')
        value = request.query_params.get('value')
        unit = request.query_params.get('unit')
        startDate = request.query_params.get('startDate')
        endDate = request.query_params.get('endDate')
        complianceStandard = request.query_params.get('complianceStandard')
        userId = request.query_params.get('userId')
        ordering = request.query_params.get('ordering')
        
        if route__id:
            queryset = queryset.filter(route__id=route__id)
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        if parameter:
            queryset = queryset.filter(parameter__icontains=parameter)
        if value:
            queryset = queryset.filter(value=value)
        if unit:
            queryset = queryset.filter(unit__icontains=unit)
        if startDate:
            queryset = queryset.filter(measurementDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(measurementDate__lte=convert_str_to_date_max_time(endDate))
        if complianceStandard:
            queryset = queryset.filter(complianceStandard__icontains=complianceStandard)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ordering:
            queryset = queryset.order_by(ordering)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['POST'], url_path='import')
    def import_environment(self, request):
        """
        Import Environment data from list data
        Request Body:
        [
            {
                "parameter": "ตัวอย่างพารามิเตอร์สิ่งแวดล้อม",
                "value": "ตัวอย่างค่าที่วัดได้",
                "unit": "ตัวอย่างหน่วยวัด",
                "measurementDate": "ตัวอย่างวันที่วัด",
                "route": "ตัวอย่างข้อมูลเส้นทางการเดินรถ",
                "province": "ตัวอย่างจังหวัด",
                "district": "ตัวอย่างอำเภอ",
                "complianceStandard": "ตัวอย่างมาตรฐานสิ่งแวดล้อมที่อ้างอิง",
                "remark": "ตัวอย่างหมายเหตุ"
            }
        ]
        """
        created_environments = []

        try:
            for environment_data in request.data:
                from mas.models import MasProvince, MasDistrict
                from service_fares.models import Route as Route

                # Handle route lookup
                route_obj = None
                if environment_data.get('route'):
                    route_value = environment_data.get('route')
                    try:
                        if isinstance(route_value, int) or (isinstance(route_value, str) and route_value.isdigit()):
                            route_obj = Route.objects.get(id=int(route_value))
                        else:
                            route_obj = Route.objects.filter(name__icontains=route_value).first()
                    except:
                        route_obj = None

                # Handle province lookup
                province_obj = None
                if environment_data.get('province'):
                    province_value = environment_data.get('province')
                    try:
                        if isinstance(province_value, int) or (isinstance(province_value, str) and province_value.isdigit()):
                            province_obj = MasProvince.objects.get(id=int(province_value))
                        else:
                            province_obj = MasProvince.objects.filter(name__icontains=province_value).first()
                    except:
                        province_obj = None

                # Handle district lookup
                district_obj = None
                if environment_data.get('district'):
                    district_value = environment_data.get('district')
                    try:
                        if isinstance(district_value, int) or (isinstance(district_value, str) and district_value.isdigit()):
                            district_obj = MasDistrict.objects.get(id=int(district_value))
                        else:
                            district_obj = MasDistrict.objects.filter(name__icontains=district_value).first()
                    except:
                        district_obj = None

                # Handle value conversion
                value = 0
                try:
                    value_data = environment_data.get('value', 0)
                    if isinstance(value_data, (int, float)):
                        value = float(value_data)
                    elif isinstance(value_data, str):
                        # Try to convert string to float, default to 0 if fails
                        try:
                            value = float(value_data)
                        except:
                            value = 0
                except:
                    value = 0

                # Handle measurementDate conversion
                from datetime import datetime
                measurement_date = None
                try:
                    date_data = environment_data.get('measurementDate')
                    if date_data:
                        if isinstance(date_data, str):
                            # Try different date formats
                            date_formats = [
                                '%Y-%m-%d',
                                '%Y-%m-%dT%H:%M:%S',
                                '%Y-%m-%d %H:%M:%S',
                                '%d/%m/%Y',
                                '%d-%m-%Y'
                            ]
                            for date_format in date_formats:
                                try:
                                    measurement_date = datetime.strptime(date_data, date_format)
                                    break
                                except:
                                    continue
                            # If no format worked, use current datetime
                            if measurement_date is None:
                                measurement_date = datetime.now()
                        else:
                            measurement_date = date_data
                    else:
                        measurement_date = datetime.now()
                except:
                    measurement_date = datetime.now()

                # Create environment record directly without validation
                environment = Environment.objects.create(
                    parameter=environment_data.get('parameter', ''),
                    value=value,
                    unit=environment_data.get('unit', ''),
                    measurementDate=measurement_date,
                    route=route_obj,
                    masProvince=province_obj,
                    masDistrict=district_obj,
                    complianceStandard=environment_data.get('complianceStandard', ''),
                    remark=environment_data.get('remark', '')
                )

                created_environments.append({
                    "id": environment.id,
                    "parameter": environment.parameter,
                    "value": environment.value,
                    "unit": environment.unit,
                    "measurementDate": environment.measurementDate.isoformat() if environment.measurementDate else None,
                    "complianceStandard": environment.complianceStandard,
                    "remark": environment.remark
                })

            return Response(
                {
                    "message": f"Import completed. Created {len(created_environments)} environment records.",
                    "created_environments": created_environments,
                    "created_count": len(created_environments),
                    "total_count": len(request.data)
                },
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return Response(
                {
                    "error": "Import failed",
                    "message": str(e),
                    "created_count": len(created_environments)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='export-excel')
    def export_excel(self, request, *args, **kwargs):
        """
        <h1>ดาวน์โหลดข้อมูลด้านสิ่งแวดล้อม</h1>
        """
        
        ids = request.data.get('ids')
        route__id = request.data.get('route__id')
        masProvince__id = request.data.get('masProvince__id')
        masDistrict__id = request.data.get('masDistrict__id')
        parameter = request.data.get('parameter')
        value = request.data.get('value')
        unit = request.data.get('unit')
        startDate = request.data.get('startDate')
        endDate = request.data.get('endDate')
        complianceStandard = request.data.get('complianceStandard')
        userId = request.data.get('userId')
        
        queryset = self.get_queryset()
        if route__id:
            queryset = queryset.filter(route__id=route__id)
        if masProvince__id:
            queryset = queryset.filter(masProvince__id=masProvince__id)
        if masDistrict__id:
            queryset = queryset.filter(masDistrict__id=masDistrict__id)
        if parameter:
            queryset = queryset.filter(parameter__icontains=parameter)
        if value:
            queryset = queryset.filter(value=value)
        if unit:
            queryset = queryset.filter(unit__icontains=unit)
        if startDate:
            queryset = queryset.filter(measurementDate__gte=convert_str_to_date_min_time(startDate))
        if endDate:
            queryset = queryset.filter(measurementDate__lte=convert_str_to_date_max_time(endDate))
        if complianceStandard:
            queryset = queryset.filter(complianceStandard__icontains=complianceStandard)
        if userId:
            queryset = queryset.filter(
                Q(updateUserId__isnull=True, createUserId=userId) |
                Q(updateUserId=userId)
            )
        if ids:
            if -1 in ids:
                queryset = queryset.exclude(id__in=[id for id in ids if id != -1])
            else:
                queryset = queryset.filter(id__in=ids)
                
        for item in queryset:
            item.updateUser = User.objects.get(id=item.updateUserId) if item.updateUserId else None
            item.createUser = User.objects.get(id=item.createUserId)
        
        from mas.models import MasProvince, MasDistrict
        from service_fares.models import Route
        mas_province_mapping = {obj.id: obj.name for obj in MasProvince.objects.all()}
        mas_district_mapping = {obj.id: obj.name for obj in MasDistrict.objects.all()}
        route_mapping = {obj.id: obj.name for obj in Route.objects.all()}

        # Convert data to DataFrame
        excel_data = []
        count = 1
        for item in queryset:
            excel_data.append({
                'ลำดับ': count,
                'พารามิเตอร์สิ่งแวดล้อม': item.parameter,
                'ค่าที่วัดได้': item.value,
                'หน่วยวัด': item.unit,
                'วันที่วัด': item.measurementDate.strftime('%d/%m/%Y') if item.measurementDate else None,
                'ข้อมูลเเส้นทางการเดินรถ': route_mapping.get(item.route.id, item.route.name) if item.route else None,
                'จังหวัด': mas_province_mapping.get(item.masProvince.id, item.masProvince.name) if item.masProvince else None,
                'อำเภอ': mas_district_mapping.get(item.masDistrict.id, item.masDistrict.name) if item.masDistrict else None,
                'มาตรฐานสิ่งแวดล้อมที่อ้างอิง': item.complianceStandard,
                'หมายเหตุ': item.remark,
                'ผู้แก้ไขล่าสุด': item.updateUser.firstname + " " + item.updateUser.lastname if item.updateUser else item.createUser.firstname + " " + item.createUser.lastname,
                'วันที่แก้ไขล่าสุด': item.updateDate.strftime('%d/%m/%Y') if item.updateUser else item.createDate.strftime('%d/%m/%Y'),
            })
            count += 1

        df = pd.DataFrame(excel_data)
        sheet_name = 'ข้อมูลด้านสิ่งแวดล้อม'
        # Create Excel file
        excel_file = io.BytesIO()
        with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, startrow=2)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']
            
            # Add header text
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Add the Thai header text
            worksheet.merge_range('A1:L1', sheet_name, header_format)
            
            # Add the date subheader with current date in Thai format
            # Thai month abbreviations
            thai_months = {
                1: 'ม.ค.',
                2: 'ก.พ.',
                3: 'มี.ค.',
                4: 'เม.ย.',
                5: 'พ.ค.',
                6: 'มิ.ย.',
                7: 'ก.ค.',
                8: 'ส.ค.',
                9: 'ก.ย.',
                10: 'ต.ค.',
                11: 'พ.ย.',
                12: 'ธ.ค.'
            }
            
            # Get current date
            now = datetime.now()
            # Convert to Thai Buddhist Era (BE) by adding 543 years
            thai_year = now.year + 543
            # Format the date string in Thai format
            thai_date = f"(ดาวน์โหลด ณ วันที่ {now.day} {thai_months[now.month]} {thai_year})"
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter'
            })
            worksheet.merge_range('A2:L2', thai_date, date_format)
            
            # Format for text cells that allows text wrapping
            wrap_format = workbook.add_format({
                'text_wrap': True,
                'valign': 'top'
            })
            
            # Auto-adjust columns' width
            for i, col in enumerate(df.columns):
                # Calculate appropriate column width
                # For multi-line cells, consider the longest line
                max_width = len(col) + 2  # Start with column header width + padding
                for value in df[col]:
                    if value and isinstance(value, str):
                        # For multi-line content, check each line
                        lines = str(value).split('\n')
                        for line in lines:
                            # Thai characters may need more width than latin characters
                            # Multiplier can be adjusted based on font characteristics
                            thai_char_count = sum(1 for c in line if '\u0E00' <= c <= '\u0E7F')
                            latin_char_count = len(line) - thai_char_count
                            adjusted_width = latin_char_count + (thai_char_count * 1.5)
                            max_width = max(max_width, adjusted_width)
                
                # Add some padding and set column width (maximum width of 100)
                worksheet.set_column(i, i, min(max_width, 100))
            
            # Apply text wrapping format to all data cells and adjust row heights
            for row_num in range(3, len(df) + 3):  # +3 because data starts at row 3 (after headers)
                # Set row height to autofit the contents
                max_lines = 1
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num-3, col_num]
                    if cell_value and isinstance(cell_value, str):
                        lines = cell_value.count('\n') + 1
                        max_lines = max(max_lines, lines)
                    worksheet.write(row_num, col_num, cell_value, wrap_format)
                
                # Set the row height based on the maximum number of lines (approximate 15 points per line)
                row_height = max_lines * 15
                worksheet.set_row(row_num, row_height)
            
            # Set header rows height
            worksheet.set_row(0, 25)  # Title row
            worksheet.set_row(1, 20)  # Date row
        
        excel_file.seek(0)
        
        # Create response
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # Encode the Thai filename for better cross-browser compatibility
        filename = f'{sheet_name.replace("/", "_")}.xlsx'
        
        # RFC 5987 encoding for non-ASCII filenames
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
        content_disposition = f"attachment; filename*=UTF-8''{encoded_filename}"
        
        response['Content-Disposition'] = content_disposition
        
        return response
